mod app_handler;
mod window;

pub use app_handler::App<PERSON><PERSON><PERSON>;

use crate::backend::driver::DriverFactory;
use crate::backend::driver_enums::Backend;
use crate::backend::platform::Platform;
use crate::engine::Engine;
use std::any::Any;
use std::sync::{Arc, Mutex};
use tracing::{debug, error};
use winit::event_loop::{ControlFlow, EventLoop};

/// App 结构体用于管理应用程序的生命周期
pub struct App {
    event_loop: EventLoop<()>,
}

impl App {
    /// 创建一个新的应用程序实例
    pub fn new(backend: Backend) -> Result<Self, String> {
        // 创建事件循环
        let event_loop = EventLoop::new().map_err(|e| {
            error!(error = ?e, "Failed to create event loop");
            format!("Failed to create event loop: {}", e)
        })?;

        Ok(Self { event_loop })
    }

    /// 运行应用程序
    pub fn run(self) {
        // 设置事件循环
        self.event_loop.set_control_flow(ControlFlow::Poll);

        // 运行事件循环 - 窗口将在AppHandler的resumed方法中创建
        let mut app_handler = AppHandler::new();
        let _ = self.event_loop.run_app(&mut app_handler);
    }
}
