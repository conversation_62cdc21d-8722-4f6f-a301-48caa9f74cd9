use crate::engine::{Engine};
use tracing::{debug, error, info};
use winit::{
    event_loop::ActiveEventLoop,
    window::{Window, WindowId, WindowAttributes},
    event::WindowEvent,
    dpi::PhysicalSize,
};

/// AppHandler 处理应用程序事件
pub struct AppHandler {
    pub window: Option<Window>,
  
}

impl AppHandler {
    pub fn new() -> Self {
        Self {
            window: None,
           
        }
    }
}

impl winit::application::ApplicationHandler for AppHandler {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        // 应用程序恢复时的处理
        if self.window.is_none() {
            // 创建窗口
            let window_attributes = WindowAttributes::default()
                .with_title("Moonfield Engine Demo")
                .with_inner_size(PhysicalSize::new(800, 600));

            match event_loop.create_window(window_attributes) {
                Ok(window) => {
                    self.window = Some(window);

                    
                }
                Err(err) => {
                    error!("Failed to create window: {}", err);
                }
            }
        }
    }

    fn window_event(
        &mut self,
        event_loop: &ActiveEventLoop,
        _window_id: WindowId,
        event: WindowEvent,
    ) {
        match event {
            WindowEvent::CloseRequested => {
                debug!("Window close requested");

                // 直接退出，让Drop实现处理资源清理
                // 这样可以避免在这里和Drop中重复清理资源
                event_loop.exit();
            }
            WindowEvent::Resized(_) => {
                // 处理窗口大小变化
            }
            // 简化键盘输入处理，使用键盘事件
            WindowEvent::KeyboardInput { .. } => {}
            _ => {}
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        
    }
}

impl Drop for AppHandler {
    fn drop(&mut self) {
        debug!("Dropping AppHandler");

      
    }
}