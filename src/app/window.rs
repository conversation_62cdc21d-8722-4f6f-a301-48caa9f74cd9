use winit::{
    event_loop::ActiveEventLoop,
    window::{Window, WindowAttributes},
    dpi::PhysicalSize,
};

/// 创建一个窗口
pub fn create_window(
    event_loop: &ActiveEventLoop,
    title: &str,
    width: u32,
    height: u32,
) -> Result<Window, winit::error::OsError> {
    event_loop.create_window(
        WindowAttributes::default()
            .with_title(title)
            .with_inner_size(PhysicalSize::new(width, height)),
    )
}

