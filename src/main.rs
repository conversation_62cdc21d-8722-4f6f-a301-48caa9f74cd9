mod utils;

mod backend;
mod engine;
mod app;
mod ecs;

use backend::driver_enums::Backend;
use app::App;
use tracing::error;

fn main() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 创建应用程序
    let app = match App::new(Backend::Vulkan) {
        Ok(app) => app,
        Err(err) => {
            error!("Failed to create application: {}", err);
            return;
        }
    };

    // 运行应用程序
    app.run();
}
