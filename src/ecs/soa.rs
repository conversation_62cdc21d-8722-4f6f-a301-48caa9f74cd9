//! Structure of Arrays (SOA) implementation.
//!
//! This module provides a generic Structure of Arrays (SOA) implementation,
//! which stores components in separate arrays for better cache locality.
//! Inspired by Filament's StructureOfArrays.

use std::marker::PhantomData;
use std::mem::{align_of, size_of};
use std::ptr::NonNull;
use std::alloc::Layout;
use crate::utils::allocator::{Allocator, pointermath};
use crate::utils::allocator::heap::HeapAllocator;

/// Structure of Arrays (SOA) implementation.
///
/// This struct stores multiple arrays of different types in a single memory block,
/// with each array having the same length. This allows for better cache locality when
/// processing elements of the same type.
pub struct SoA<A: Allocator = HeapAllocator> {
    // 单一内存块指针
    buffer: Option<NonNull<u8>>,
    // 指向各个数组的指针
    arrays: Vec<*mut u8>,
    // 数组元素大小
    element_sizes: Vec<usize>,
    // 数组元素对齐
    element_aligns: Vec<usize>,
    // 当前元素数量
    size: usize,
    // 当前容量
    capacity: usize,
    // 分配器
    allocator: A,
}

impl<A: Allocator> SoA<A> {
    /// Creates a new empty SOA with the given allocator.
    pub fn new_with_allocator(allocator: A) -> Self {
        Self {
            buffer: None,
            arrays: Vec::new(),
            element_sizes: Vec::new(),
            element_aligns: Vec::new(),
            size: 0,
            capacity: 0,
            allocator,
        }
    }

    /// Creates a new SOA with the given capacity and allocator.
    pub fn with_capacity_and_allocator(capacity: usize, allocator: A) -> Self {
        let mut soa = Self::new_with_allocator(allocator);
        if capacity > 0 {
            soa.ensure_capacity(capacity);
        }
        soa
    }

    /// Returns the current size (number of elements) in the SOA.
    pub fn size(&self) -> usize {
        self.size
    }

    /// Returns the current capacity of the SOA.
    pub fn capacity(&self) -> usize {
        self.capacity
    }

    /// Calculates the offsets for each array in the buffer.
    fn calculate_offsets(&self, capacity: usize) -> Vec<usize> {
        let mut offsets = Vec::with_capacity(self.arrays.len());
        let mut current_offset = 0;

        for i in 0..self.element_sizes.len() {
            // 对齐当前偏移量
            let alignment = self.element_aligns[i];
            let remainder = current_offset % alignment;
            if remainder != 0 {
                current_offset += alignment - remainder;
            }

            offsets.push(current_offset);
            current_offset += self.element_sizes[i] * capacity;
        }

        offsets
    }

    /// Calculates the total size needed for the buffer.
    fn calculate_buffer_size(&self, capacity: usize) -> usize {
        if self.element_sizes.is_empty() {
            return 0;
        }

        let offsets = self.calculate_offsets(capacity);
        let last_index = self.element_sizes.len() - 1;
        offsets[last_index] + (self.element_sizes[last_index] * capacity)
    }

    /// Ensures that the SOA has at least the specified capacity.
    pub fn ensure_capacity(&mut self, capacity: usize) {
        if capacity <= self.capacity {
            return;
        }

        // 计算新容量
        let new_capacity = capacity.max(self.capacity * 2).max(8);
        self.resize_capacity(new_capacity);
    }

    /// Resizes the capacity of the SOA.
    fn resize_capacity(&mut self, new_capacity: usize) {
        if new_capacity == self.capacity {
            return;
        }

        // 如果没有数组，不需要分配内存
        if self.element_sizes.is_empty() {
            self.capacity = new_capacity;
            return;
        }

        // 计算新缓冲区大小
        let buffer_size = self.calculate_buffer_size(new_capacity);

        // 计算最大对齐要求
        let max_align = self.element_aligns.iter().max().copied().unwrap_or(1);

        // 分配新内存
        let new_buffer = self.allocator.alloc(buffer_size, max_align, 0)
            .expect("Failed to allocate memory for SOA");

        let new_buffer = unsafe { NonNull::new_unchecked(new_buffer) };

        // 计算新偏移量
        let new_offsets = self.calculate_offsets(new_capacity);

        // 如果有旧数据，复制到新缓冲区
        if let Some(old_buffer) = self.buffer {
            let old_offsets = self.calculate_offsets(self.capacity);

            // 复制每个数组的数据
            for i in 0..self.arrays.len() {
                let element_size = self.element_sizes[i];
                let old_array = unsafe { old_buffer.as_ptr().add(old_offsets[i]) };
                let new_array = unsafe { new_buffer.as_ptr().add(new_offsets[i]) };

                // 复制数据
                unsafe {
                    std::ptr::copy_nonoverlapping(
                        old_array,
                        new_array,
                        element_size * self.size,
                    );
                }
            }

            // 释放旧缓冲区
            let old_buffer_size = self.calculate_buffer_size(self.capacity);
            unsafe {
                self.allocator.free(old_buffer.as_ptr(), old_buffer_size);
            }
        }

        // 更新数组指针
        self.arrays.clear();
        for i in 0..self.element_sizes.len() {
            let array_ptr = unsafe { new_buffer.as_ptr().add(new_offsets[i]) };
            self.arrays.push(array_ptr);
        }

        // 更新缓冲区和容量
        self.buffer = Some(new_buffer);
        self.capacity = new_capacity;
    }

    /// Adds a new array to the SOA.
    pub fn add_array<E>(&mut self) -> usize {
        let element_size = size_of::<E>();
        let element_align = align_of::<E>();

        // 保存元素大小和对齐
        self.element_sizes.push(element_size);
        self.element_aligns.push(element_align);

        // 如果还没有分配内存，或者需要重新分配
        if self.buffer.is_none() || self.capacity == 0 {
            // 初始容量为8
            self.resize_capacity(8);
        } else {
            // 需要重新分配内存以容纳新数组
            self.resize_capacity(self.capacity);
        }

        // 获取新数组的索引
        let index = self.arrays.len() - 1;

        index
    }

    /// Returns a reference to the element at the given index in the array.
    pub fn get<E>(&self, array_index: usize, element_index: usize) -> &E {
        assert!(array_index < self.arrays.len(), "Array index out of bounds");
        assert!(element_index < self.size, "Element index out of bounds");

        unsafe {
            let ptr = self.arrays[array_index] as *mut E;
            &*ptr.add(element_index)
        }
    }

    /// Returns a mutable reference to the element at the given index in the array.
    pub fn get_mut<E>(&mut self, array_index: usize, element_index: usize) -> &mut E {
        assert!(array_index < self.arrays.len(), "Array index out of bounds");
        assert!(element_index < self.size, "Element index out of bounds");

        unsafe {
            let ptr = self.arrays[array_index] as *mut E;
            &mut *ptr.add(element_index)
        }
    }

    /// Adds a new element to the end of each array.
    pub fn push(&mut self) {
        self.ensure_capacity(self.size + 1);
        self.size += 1;
    }

    /// Removes the last element from each array.
    pub fn pop(&mut self) {
        if self.size > 0 {
            self.size -= 1;
        }
    }

    /// Clears all arrays, setting the size to 0.
    pub fn clear(&mut self) {
        self.size = 0;
    }

    /// Returns a pointer to the raw array data for the given array index.
    pub fn data<E>(&self, array_index: usize) -> *const E {
        assert!(array_index < self.arrays.len(), "Array index out of bounds");
        self.arrays[array_index] as *const E
    }

    /// Returns a mutable pointer to the raw array data for the given array index.
    pub fn data_mut<E>(&mut self, array_index: usize) -> *mut E {
        assert!(array_index < self.arrays.len(), "Array index out of bounds");
        self.arrays[array_index] as *mut E
    }

    /// Returns a slice of the array data for the given array index.
    pub fn slice<E>(&self, array_index: usize) -> &[E] {
        assert!(array_index < self.arrays.len(), "Array index out of bounds");
        unsafe {
            std::slice::from_raw_parts(self.data::<E>(array_index), self.size)
        }
    }

    /// Returns a mutable slice of the array data for the given array index.
    pub fn slice_mut<E>(&mut self, array_index: usize) -> &mut [E] {
        assert!(array_index < self.arrays.len(), "Array index out of bounds");
        unsafe {
            std::slice::from_raw_parts_mut(self.data_mut::<E>(array_index), self.size)
        }
    }

    // 关联类型在当前Rust版本中不稳定，暂时注释掉
    // pub type TypeAt<const N: usize> = ();
}

/// Field accessor for SOA elements
pub struct Field<'a, A: Allocator, E, I = usize> {
    soa: &'a mut SoA<A>,
    array_index: usize,
    element_index: I,
    _marker: PhantomData<E>,
}

impl<'a, A: Allocator, E, I: Into<usize> + Copy> Field<'a, A, E, I> {
    /// Creates a new field accessor.
    pub fn new(soa: &'a mut SoA<A>, array_index: usize, element_index: I) -> Self {
        Self {
            soa,
            array_index,
            element_index,
            _marker: PhantomData,
        }
    }

    /// Gets a reference to the field.
    pub fn get(&self) -> &E {
        self.soa.get::<E>(self.array_index, self.element_index.into())
    }

    /// Gets a mutable reference to the field.
    pub fn get_mut(&mut self) -> &mut E {
        self.soa.get_mut::<E>(self.array_index, self.element_index.into())
    }
}

impl<A: Allocator + Default> SoA<A> {
    /// Creates a new empty SOA with the default allocator.
    pub fn new() -> Self {
        Self::new_with_allocator(A::default())
    }

    /// Creates a new SOA with the given capacity and default allocator.
    pub fn with_capacity(capacity: usize) -> Self {
        Self::with_capacity_and_allocator(capacity, A::default())
    }
}

impl<A: Allocator> Drop for SoA<A> {
    fn drop(&mut self) {
        // 释放分配的内存
        if let Some(buffer) = self.buffer {
            let buffer_size = self.calculate_buffer_size(self.capacity);
            unsafe {
                self.allocator.free(buffer.as_ptr(), buffer_size);
            }
        }
    }
}
