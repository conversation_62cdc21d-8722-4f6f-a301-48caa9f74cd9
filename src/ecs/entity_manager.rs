use crate::ecs::entity::{Entity, EntityType};
use std::collections::{HashSet, VecDeque};

/// - combine as the entity ID：entity_id = (generation << GENERATION_SHIFT) | index;
/// - get index：index = entity_id & INDEX_MASK;
/// - get generation：generation = entity_id >> GENERATION_SHIFT;
const GENERATION_SHIFT: u32 = 17; // use lower 17 bits for index
const RAW_INDEX_COUNT: u32 = 1 << GENERATION_SHIFT;
const INDEX_MASK: u32 = RAW_INDEX_COUNT - 1;

const MIN_FREE_INDICES: usize = 1024;

pub(crate) trait EntityListener: Send + Sync + 'static {
    fn on_entity_destroyed(&self, entity: &[Entity]);
}

pub struct EntityManager {
    gens: Box<[u8]>,
    current_index: u32,
    free_list_lock: std::sync::Mutex<()>,
    free_list: VecDeque<EntityType>,
    listener_lock: std::sync::Mutex<()>,
    listeners: HashSet<Box<dyn EntityListener>>,
}

impl EntityManager {
    fn get_generation(e: Entity) -> EntityType {
        e.id() >> GENERATION_SHIFT
    }

    fn get_index(e: Entity) -> EntityType {
        e.id() & INDEX_MASK
    }

    fn make_identity(generation: EntityType, index: EntityType) -> EntityType {
        (generation << GENERATION_SHIFT) | index
    }

    fn get_listeners(&self) -> Vec<Box<dyn EntityListener>> {
        let _guard = self.listener_lock.lock().unwrap();
        let listeners = self.listeners.iter().cloned().collect();
        listeners
    }
}

impl EntityManager {
    pub fn get() {}

    /// maximum number of entities that can exist at the same time
    pub fn get_max_entity_count() -> usize {
        // because index 0 is reserved, we only have 2^GENERATION_SHIFT - 1 valid indices
        (RAW_INDEX_COUNT - 1) as usize
    }

    /// number of active entities
    pub fn get_entity_count(&self) -> usize {
        let _ = self.free_list_lock.lock().unwrap();
        if self.current_index < RAW_INDEX_COUNT {
            (self.current_index - 1u32) as usize - self.free_list.len()
        } else {
            Self::get_max_entity_count() - self.free_list.len()
        }
    }

    /// create n entities. Thread Safe
    pub fn create_n(&mut self, n: usize, entities: &mut [Entity]) {
        let gens = &self.gens;

        let _guard = self.free_list_lock.lock().unwrap();

        let mut current_index_val = self.current_index;

        // available indexes
        let free_list = &mut self.free_list;

        for entity_slot in entities.iter_mut().take(n) {
            let acquired_index_option: Option<EntityType> =
                if current_index_val < RAW_INDEX_COUNT && free_list.len() < MIN_FREE_INDICES {
                    let index_to_use = current_index_val;
                    current_index_val += 1;
                    Some(index_to_use)
                } else {
                    free_list.pop_front()
                };

            if let Some(index_to_use) = acquired_index_option {
                entity_slot.set_id(Self::make_identity(
                    gens[index_to_use as usize] as EntityType,
                    index_to_use,
                ));
            } else {
                *entity_slot = Entity::default()
            }
        }

        self.current_index = current_index_val;
    }

    /// destroy n entities. Thread Safe
    pub fn destroy_n(&self, n: usize, entities: &mut [Entity]) {
        let freelist = &mut self.free_list;
        let gens = &mut self.gens;

        let free_list_lock = self.free_list_lock.lock().unwrap();
        for entity in entities.iter_mut() {
            if entity.is_valid() {
                continue;
            }

            assert!(self.is_alive(*entity));

            if self.is_alive(*entity) {
                let index = Self::get_index(*entity);
                freelist.push_back(index);
                gens[index as usize] += 1;
            }
        }
        free_list_lock.unlock();
    }

    /// create a new entity. Thread Safe
    /// Return Entity.is_null() if the entity cannot be allocated.
    pub fn create(&self) -> Entity {
        let e = Entity::default();
        Self::create_n(&self, 1, &mut [e]);
        e
    }

    pub fn destroy(&self, entity: Entity) {
        Self::destroy_n(&self, 1, &mut [entity]);
    }

    pub fn is_alive(&self, entity: Entity) -> bool {}

    pub fn register_listener(&self, listener: Box<dyn EntityListener>) {}

    pub fn unregister_listener(&self, listener: &dyn EntityListener) {}

    pub fn get_generation_for_index(index: usize) {}
}
