//! Component manager for the ECS system.
//!
//! This module provides the SingleInstanceComponentManager, which is a base class for
//! component managers that allow only one instance of a component per entity.
//! Inspired by Filament's SingleInstanceComponentManager.

use std::collections::HashMap;
use std::marker::PhantomData;
use std::any::Any;

use crate::ecs::entity::{Entity, EntityManager, EntityManagerSingleton};
use crate::ecs::soa::SoA;

/// A type-erased entity instance.
#[derive(Debug, Copy, Clone, Eq, PartialEq, Hash)]
pub struct EntityInstance(pub(crate) u32);

impl EntityInstance {
    /// The null instance (invalid instance reference).
    pub const NULL: EntityInstance = EntityInstance(0);

    /// Creates a new instance with the given index.
    pub const fn new(index: u32) -> Self {
        EntityInstance(index)
    }

    /// Returns true if this is a valid instance.
    pub fn is_valid(self) -> bool {
        self.0 != 0
    }
}

/// A single-instance component manager.
///
/// This is a base class for component managers that allow only one instance of a component per entity.
/// It handles the mapping between entities and component instances, as well as the storage of
/// component data in a structure-of-arrays format.
pub struct SingleInstanceComponentManager<T: 'static> {
    /// Maps entities to component instances.
    instance_map: HashMap<Entity, EntityInstance>,
    /// Maps component instances to entities.
    entity_map: Vec<Entity>,
    /// The component data.
    data: SoA<T>,
    /// Marker for the component type.
    _marker: PhantomData<T>,
}

impl<T: 'static> SingleInstanceComponentManager<T> {
    /// Creates a new component manager.
    pub fn new() -> Self {
        let mut data = SoA::new();
        
        // Add a dummy entry at index 0 (reserved for null instance)
        data.push();
        
        Self {
            instance_map: HashMap::new(),
            entity_map: vec![Entity::NULL],
            data,
            _marker: PhantomData,
        }
    }

    /// Returns true if the given entity has a component.
    pub fn has_component(&self, entity: Entity) -> bool {
        self.instance_map.contains_key(&entity)
    }

    /// Gets the instance for the given entity.
    pub fn get_instance(&self, entity: Entity) -> EntityInstance {
        self.instance_map.get(&entity).copied().unwrap_or(EntityInstance::NULL)
    }

    /// Gets the entity for the given instance.
    pub fn get_entity(&self, instance: EntityInstance) -> Entity {
        if instance.0 as usize >= self.entity_map.len() {
            return Entity::NULL;
        }
        self.entity_map[instance.0 as usize]
    }

    /// Returns the number of components.
    pub fn get_component_count(&self) -> usize {
        // Subtract 1 for the dummy entry at index 0
        self.data.size() - 1
    }

    /// Returns true if there are no components.
    pub fn is_empty(&self) -> bool {
        self.get_component_count() == 0
    }

    /// Returns a slice of all entities that have components.
    pub fn get_entities(&self) -> &[Entity] {
        // Skip the dummy entry at index 0
        &self.entity_map[1..]
    }

    /// Adds a component to the given entity.
    ///
    /// If the entity already has a component, returns the existing instance.
    pub fn add_component(&mut self, entity: Entity) -> EntityInstance {
        if entity.is_null() {
            return EntityInstance::NULL;
        }

        if let Some(&instance) = self.instance_map.get(&entity) {
            // Entity already has this component
            return instance;
        }

        // Add a new component
        self.data.push();
        let instance = EntityInstance::new(self.data.size() as u32 - 1);
        
        self.instance_map.insert(entity, instance);
        self.entity_map.push(entity);
        
        instance
    }

    /// Removes a component from the given entity.
    ///
    /// Returns the instance that was removed, or NULL if the entity didn't have a component.
    pub fn remove_component(&mut self, entity: Entity) -> EntityInstance {
        if let Some(instance) = self.instance_map.remove(&entity) {
            let index = instance.0 as usize;
            let last = self.data.size() - 1;
            
            if index != last {
                // Move the last component to the removed slot to keep the array compact
                // This is done by the SoA implementation
                
                // Update the entity map
                self.entity_map[index] = self.entity_map[last];
                
                // Update the instance map for the moved entity
                let moved_entity = self.entity_map[index];
                if let Some(instance_entry) = self.instance_map.get_mut(&moved_entity) {
                    *instance_entry = EntityInstance::new(index as u32);
                }
            }
            
            // Remove the last entry
            self.entity_map.pop();
            self.data.pop();
            
            return instance;
        }
        
        EntityInstance::NULL
    }

    /// Performs garbage collection on the component manager.
    ///
    /// This removes components for entities that are no longer alive.
    pub fn gc(&mut self, entity_manager: &EntityManager) {
        let mut to_remove = Vec::new();
        
        for (&entity, _) in &self.instance_map {
            if !entity_manager.is_alive(entity) {
                to_remove.push(entity);
            }
        }
        
        for entity in to_remove {
            self.remove_component(entity);
        }
    }

    /// Gets a reference to the component data.
    pub fn get_data(&self) -> &SoA<T> {
        &self.data
    }

    /// Gets a mutable reference to the component data.
    pub fn get_data_mut(&mut self) -> &mut SoA<T> {
        &mut self.data
    }
}

/// A macro to create a component manager with named fields.
#[macro_export]
macro_rules! define_component_manager {
    ($(#[$attr:meta])* $vis:vis struct $name:ident {
        $($(#[$field_attr:meta])* $field_vis:vis $field_name:ident: $field_type:ty),* $(,)?
    }) => {
        $(#[$attr])*
        $vis struct $name {
            manager: $crate::ecs::SingleInstanceComponentManager<$name>,
            data: $crate::define_soa! {
                struct ComponentData {
                    $($(#[$field_attr])* $field_vis $field_name: $field_type),*
                }
            },
        }

        impl $name {
            /// Creates a new component manager.
            pub fn new() -> Self {
                Self {
                    manager: $crate::ecs::SingleInstanceComponentManager::new(),
                    data: ComponentData::new(),
                }
            }

            /// Returns true if the given entity has a component.
            pub fn has_component(&self, entity: $crate::ecs::Entity) -> bool {
                self.manager.has_component(entity)
            }

            /// Gets the instance for the given entity.
            pub fn get_instance(&self, entity: $crate::ecs::Entity) -> $crate::ecs::EntityInstance {
                self.manager.get_instance(entity)
            }

            /// Returns the number of components.
            pub fn get_component_count(&self) -> usize {
                self.manager.get_component_count()
            }

            /// Returns true if there are no components.
            pub fn is_empty(&self) -> bool {
                self.manager.is_empty()
            }

            /// Returns a slice of all entities that have components.
            pub fn get_entities(&self) -> &[$crate::ecs::Entity] {
                self.manager.get_entities()
            }

            /// Adds a component to the given entity.
            pub fn add_component(&mut self, entity: $crate::ecs::Entity) -> $crate::ecs::EntityInstance {
                let instance = self.manager.add_component(entity);
                if instance.is_valid() && instance.0 as usize >= self.data.size() {
                    self.data.push();
                }
                instance
            }

            /// Removes a component from the given entity.
            pub fn remove_component(&mut self, entity: $crate::ecs::Entity) -> $crate::ecs::EntityInstance {
                let instance = self.manager.remove_component(entity);
                if instance.is_valid() {
                    // The data is automatically compacted by the SingleInstanceComponentManager
                    // We just need to make sure our data stays in sync
                    if self.data.size() > self.manager.get_component_count() + 1 {
                        self.data.pop();
                    }
                }
                instance
            }

            /// Performs garbage collection on the component manager.
            pub fn gc(&mut self, entity_manager: &$crate::ecs::EntityManager) {
                self.manager.gc(entity_manager);
                // Ensure data size matches component count
                while self.data.size() > self.manager.get_component_count() + 1 {
                    self.data.pop();
                }
            }

            /// Gets a reference to the component field for the given instance.
            $(
            $(#[$field_attr])*
            $field_vis fn $field_name(&self, instance: $crate::ecs::EntityInstance) -> &$field_type {
                assert!(instance.is_valid(), "Invalid instance");
                self.data.$field_name(instance.0 as usize)
            }
            )*

            /// Gets a mutable reference to the component field for the given instance.
            $(
            $(#[$field_attr])*
            $field_vis fn $field_name\_mut(&mut self, instance: $crate::ecs::EntityInstance) -> &mut $field_type {
                assert!(instance.is_valid(), "Invalid instance");
                self.data.$field_name\_mut(instance.0 as usize)
            }
            )*
        }
    };
}
