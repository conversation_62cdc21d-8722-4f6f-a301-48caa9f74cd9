//! FreeList implementation for memory management.
//!
//! This module provides FreeList and AtomicFreeList implementations for
//! managing free memory blocks.

use super::pointermath;
use std::sync::atomic::{AtomicPtr, Ordering};

/// A node in a free list.
#[repr(C)]
pub struct Node {
    next: *mut Node,
}

/// A free list for managing memory blocks.
pub struct FreeList {
    head: *mut Node,
    #[cfg(debug_assertions)]
    begin: *mut u8,
    #[cfg(debug_assertions)]
    end: *mut u8,
}

impl FreeList {
    /// Create a new free list from the given memory region.
    ///
    /// # Arguments
    ///
    /// * `begin` - Pointer to the beginning of the memory region.
    /// * `end` - Pointer to the end of the memory region.
    /// * `element_size` - Size of each element in the free list.
    /// * `alignment` - Alignment of each element.
    /// * `offset` - Offset for alignment.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it manipulates raw pointers.
    /// The caller must ensure that the memory region is valid and remains valid
    /// for the lifetime of the FreeList.
    pub unsafe fn new(
        begin: *mut u8,
        end: *mut u8,
        element_size: usize,
        alignment: usize,
        offset: usize,
    ) -> Self {
        debug_assert!(begin <= end);
        debug_assert!(element_size >= std::mem::size_of::<Node>());

        let head = unsafe { Self::init(begin, end, element_size, alignment, offset) };

        Self {
            head,
            #[cfg(debug_assertions)]
            begin,
            #[cfg(debug_assertions)]
            end,
        }
    }

    /// Initialize the free list.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it manipulates raw pointers.
    unsafe fn init(
        begin: *mut u8,
        end: *mut u8,
        element_size: usize,
        alignment: usize,
        offset: usize,
    ) -> *mut Node {
        let p = unsafe { pointermath::align_with_offset_mut(begin, alignment, offset) };
        let p_plus_size = unsafe { pointermath::add_mut(p, element_size) };
        let n = unsafe { pointermath::align_with_offset_mut(
            p_plus_size,
            alignment,
            offset,
        ) };

        debug_assert!(p >= begin && p < end);
        debug_assert!(n >= begin && n < end && n > p);

        let d = n as usize - p as usize;
        let num = (end as usize - p as usize) / d;

        if num == 0 {
            return std::ptr::null_mut();
        }

        // Set first entry
        let head = p as *mut Node;

        // Link the nodes
        let mut cur = head;
        for _i in 1..num {
            let next = unsafe { pointermath::add_mut(cur as *mut u8, d) } as *mut Node;
            unsafe { (*cur).next = next; }
            cur = next;
        }

        debug_assert!(cur < end as *mut Node);
        debug_assert!(unsafe { pointermath::add_mut(cur as *mut u8, d) } <= end);
        unsafe { (*cur).next = std::ptr::null_mut(); }

        head
    }

    /// Pop a node from the free list.
    ///
    /// # Returns
    ///
    /// A pointer to the popped node, or null if the list is empty.
    pub fn pop(&mut self) -> *mut u8 {
        let head = self.head;
        if !head.is_null() {
            self.head = unsafe { (*head).next };
            #[cfg(debug_assertions)]
            debug_assert!(
                self.head.is_null() || ((self.head as *mut u8) >= self.begin && (self.head as *mut u8) < self.end)
            );
        }
        head as *mut u8
    }

    /// Push a node onto the free list.
    ///
    /// # Arguments
    ///
    /// * `ptr` - Pointer to the node to push.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it manipulates raw pointers.
    /// The caller must ensure that the pointer is valid and points to memory
    /// that can be used as a Node.
    pub unsafe fn push(&mut self, ptr: *mut u8) {
        debug_assert!(!ptr.is_null());
        #[cfg(debug_assertions)]
        debug_assert!(ptr >= self.begin && ptr < self.end);

        let node = ptr as *mut Node;
        unsafe { (*node).next = self.head; }
        self.head = node;
    }

    /// Get the first node in the free list.
    pub fn get_first(&self) -> *mut u8 {
        self.head as *mut u8
    }
}

/// A thread-safe free list using atomic operations.
pub struct AtomicFreeList {
    storage: *mut Node,
    head: AtomicPtr<Node>,
}

unsafe impl Send for AtomicFreeList {}
unsafe impl Sync for AtomicFreeList {}

impl AtomicFreeList {
    /// Create a new atomic free list from the given memory region.
    ///
    /// # Arguments
    ///
    /// * `begin` - Pointer to the beginning of the memory region.
    /// * `end` - Pointer to the end of the memory region.
    /// * `element_size` - Size of each element in the free list.
    /// * `alignment` - Alignment of each element.
    /// * `offset` - Offset for alignment.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it manipulates raw pointers.
    /// The caller must ensure that the memory region is valid and remains valid
    /// for the lifetime of the AtomicFreeList.
    pub unsafe fn new(
        begin: *mut u8,
        end: *mut u8,
        element_size: usize,
        alignment: usize,
        offset: usize,
    ) -> Self {
        debug_assert!(begin <= end);
        debug_assert!(element_size >= std::mem::size_of::<Node>());

        let p = unsafe { pointermath::align_with_offset_mut(begin, alignment, offset) };
        let p_plus_size = unsafe { pointermath::add_mut(p, element_size) };
        let n = unsafe { pointermath::align_with_offset_mut(
            p_plus_size,
            alignment,
            offset,
        ) };

        debug_assert!(p >= begin && p < end);
        debug_assert!(n >= begin && n < end && n > p);

        let d = n as usize - p as usize;
        let num = (end as usize - p as usize) / d;

        if num == 0 {
            return Self {
                storage: std::ptr::null_mut(),
                head: AtomicPtr::new(std::ptr::null_mut()),
            };
        }

        // Set first entry
        let head = p as *mut Node;
        let storage = head;

        // Link the nodes
        let mut cur = head;
        for _i in 1..num {
            let next = unsafe { pointermath::add_mut(cur as *mut u8, d) } as *mut Node;
            unsafe { (*cur).next = next; }
            cur = next;
        }

        debug_assert!(cur < end as *mut Node);
        debug_assert!(unsafe { pointermath::add_mut(cur as *mut u8, d) } <= end);
        unsafe { (*cur).next = std::ptr::null_mut(); }

        Self {
            storage,
            head: AtomicPtr::new(head),
        }
    }

    /// Pop a node from the atomic free list.
    ///
    /// # Returns
    ///
    /// A pointer to the popped node, or null if the list is empty.
    pub fn pop(&self) -> *mut u8 {
        if self.storage.is_null() {
            return std::ptr::null_mut();
        }

        let head = self.head.load(Ordering::Relaxed);
        let current_head = head;
        while !current_head.is_null() {
            let next = unsafe { (*current_head).next };
            if self.head.compare_exchange_weak(
                current_head,
                next,
                Ordering::Acquire,
                Ordering::Relaxed,
            ).is_ok() {
                break;
            }
        }

        head as *mut u8
    }

    /// Push a node onto the atomic free list.
    ///
    /// # Arguments
    ///
    /// * `ptr` - Pointer to the node to push.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it manipulates raw pointers.
    /// The caller must ensure that the pointer is valid and points to memory
    /// that can be used as a Node.
    pub unsafe fn push(&self, ptr: *mut u8) {
        debug_assert!(!ptr.is_null());
        debug_assert!(self.storage.is_null() || ptr >= self.storage as *mut u8);

        let node = ptr as *mut Node;
        let mut current_head = self.head.load(Ordering::Relaxed);

        loop {
            unsafe { (*node).next = current_head; }
            match self.head.compare_exchange_weak(
                current_head,
                node,
                Ordering::Release,
                Ordering::Relaxed,
            ) {
                Ok(_) => break,
                Err(new_head) => current_head = new_head,
            }
        }
    }

    /// Get the first node in the atomic free list.
    pub fn get_first(&self) -> *mut u8 {
        self.head.load(Ordering::Relaxed) as *mut u8
    }
}
