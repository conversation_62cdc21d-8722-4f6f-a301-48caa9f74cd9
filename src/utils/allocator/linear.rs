//! Linear memory allocator implementation.
//!
//! This module provides a linear allocator that allocates memory sequentially
//! from a memory area. It cannot free individual allocations, but can rewind
//! to a previous state or reset completely.

use super::area::Area;
use super::heap::HeapAllocator;
use super::pointermath;
use super::Allocator;

/// A linear allocator that allocates memory sequentially from a memory area.
///
/// Features:
/// - Allocates blocks linearly
/// - Cannot free individual blocks
/// - Can free top of memory back up to a specified point
/// - Doesn't call destructors
pub struct LinearAllocator<A: Area> {
    area: A,
    current: usize,
}

impl<A: Area> LinearAllocator<A> {
    /// Create a new linear allocator with the given memory area.
    pub fn new(area: A) -> Self {
        Self {
            area,
            current: 0,
        }
    }

    /// Get the current allocation pointer.
    pub fn get_current(&self) -> *mut u8 {
        unsafe { pointermath::add_mut(self.area.begin(), self.current) }
    }

    /// Rewind the allocator to the specified pointer.
    ///
    /// # Safety
    ///
    /// The pointer must be within the allocator's memory area and must have been
    /// previously returned by `get_current()`.
    pub unsafe fn rewind(&mut self, ptr: *mut u8) {
        debug_assert!(ptr >= self.area.begin() && ptr < self.area.end());
        self.current = unsafe { ptr.offset_from(self.area.begin()) } as usize;
    }

    /// Reset the allocator, freeing all allocations.
    pub fn reset(&mut self) {
        self.current = 0;
    }

    /// Get the total size of the allocator's memory area.
    pub fn size(&self) -> usize {
        self.area.size()
    }

    /// Get the amount of memory currently allocated.
    pub fn allocated(&self) -> usize {
        self.current
    }

    /// Get the amount of memory still available.
    pub fn available(&self) -> usize {
        self.size().saturating_sub(self.current)
    }
}

impl<A: Area> Allocator for LinearAllocator<A> {
    fn alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Option<*mut u8> {
        if size == 0 {
            return Some(self.get_current());
        }

        let aligned_ptr = unsafe {
            pointermath::align_with_offset_mut(self.area.begin(), alignment, self.current + offset)
        };

        let aligned_offset = unsafe { aligned_ptr.offset_from(self.area.begin()) as usize };
        let end_offset = aligned_offset - offset + size;

        if end_offset <= self.area.size() {
            let result = unsafe { pointermath::add_mut(self.area.begin(), aligned_offset - offset) };
            self.current = end_offset;
            Some(result)
        } else {
            None
        }
    }

    unsafe fn free(&mut self, _ptr: *mut u8, _size: usize) {
        // Linear allocator doesn't free individual allocations
    }
}

/// A linear allocator that falls back to heap allocation when the linear area is exhausted.
pub struct LinearAllocatorWithFallback<A: Area> {
    linear: LinearAllocator<A>,
    heap: HeapAllocator,
    heap_allocations: Vec<(*mut u8, usize)>,
}

impl<A: Area> LinearAllocatorWithFallback<A> {
    /// Create a new linear allocator with fallback using the given memory area.
    pub fn new(area: A) -> Self {
        Self {
            linear: LinearAllocator::new(area),
            heap: HeapAllocator::new(),
            heap_allocations: Vec::new(),
        }
    }

    /// Get the current allocation pointer of the linear allocator.
    pub fn get_current(&self) -> *mut u8 {
        self.linear.get_current()
    }

    /// Rewind the linear allocator to the specified pointer.
    ///
    /// # Safety
    ///
    /// The pointer must be within the linear allocator's memory area and must have been
    /// previously returned by `get_current()`.
    pub unsafe fn rewind(&mut self, ptr: *mut u8) {
        if ptr >= self.linear.area.begin() && ptr < self.linear.area.end() {
            unsafe { self.linear.rewind(ptr); }
        }
    }

    /// Reset the allocator, freeing all allocations.
    pub fn reset(&mut self) {
        self.linear.reset();

        // Free all heap allocations
        for (ptr, size) in self.heap_allocations.drain(..) {
            unsafe {
                self.heap.free(ptr, size);
            }
        }
    }

    /// Check if the given pointer was allocated from the heap.
    pub fn is_heap_allocation(&self, ptr: *mut u8) -> bool {
        ptr < self.linear.area.begin() || ptr >= self.linear.area.end()
    }
}

impl<A: Area> Allocator for LinearAllocatorWithFallback<A> {
    fn alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Option<*mut u8> {
        // Try to allocate from the linear allocator first
        if let Some(ptr) = self.linear.alloc(size, alignment, offset) {
            return Some(ptr);
        }

        // Fall back to heap allocation
        if let Some(ptr) = self.heap.alloc_aligned(size, alignment) {
            self.heap_allocations.push((ptr, size));
            Some(ptr)
        } else {
            None
        }
    }

    unsafe fn free(&mut self, _ptr: *mut u8, _size: usize) {
        // Individual allocations are not freed until reset
    }
}

impl<A: Area> Drop for LinearAllocatorWithFallback<A> {
    fn drop(&mut self) {
        self.reset();
    }
}
