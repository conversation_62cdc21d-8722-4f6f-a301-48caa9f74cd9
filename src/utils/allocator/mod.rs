//! Memory allocation utilities.
//!
//! This module provides various memory allocators and allocation utilities:
//! - `LinearAllocator`: Allocates blocks linearly, cannot free individual blocks
//! - `HeapAllocator`: Uses system allocator for memory management
//! - `PoolAllocator`: Allocates fixed-size blocks from a memory pool
//! - `FreeList`: Manages a list of free memory blocks
//! - Various tracking and area policies

pub mod area;
pub mod freelist;
pub mod heap;
pub mod linear;
pub mod pool;
pub mod tracking;
mod tests;

/// Pointer math utilities for working with raw pointers.
pub mod pointermath {

    /// Add an offset to a pointer.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn add<P, T>(a: *const P, b: T) -> *const P
    where
        T: Into<usize>,
    {
        (a as usize + b.into()) as *const P
    }

    /// Add an offset to a mutable pointer.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn add_mut<P, T>(a: *mut P, b: T) -> *mut P
    where
        T: Into<usize>,
    {
        (a as usize + b.into()) as *mut P
    }

    /// Align a pointer to the specified alignment.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn align<P>(p: *const P, alignment: usize) -> *const P {
        debug_assert!(alignment > 0 && alignment.is_power_of_two());
        ((p as usize + alignment - 1) & !(alignment - 1)) as *const P
    }

    /// Align a mutable pointer to the specified alignment.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn align_mut<P>(p: *mut P, alignment: usize) -> *mut P {
        debug_assert!(alignment > 0 && alignment.is_power_of_two());
        ((p as usize + alignment - 1) & !(alignment - 1)) as *mut P
    }

    /// Align a pointer to the specified alignment with an offset.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn align_with_offset<P>(p: *const P, alignment: usize, offset: usize) -> *const P {
        let p_offset = unsafe { add(p, offset) };
        let result = unsafe { align(p_offset, alignment) };
        debug_assert!(result >= p_offset);
        result
    }

    /// Align a mutable pointer to the specified alignment with an offset.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it performs raw pointer arithmetic.
    /// The caller must ensure that the resulting pointer is valid.
    #[inline]
    pub unsafe fn align_with_offset_mut<P>(p: *mut P, alignment: usize, offset: usize) -> *mut P {
        let p_offset = unsafe { add_mut(p, offset) };
        let result = unsafe { align_mut(p_offset, alignment) };
        debug_assert!(result >= p_offset);
        result
    }
}

/// Trait for memory allocators.
pub trait Allocator {
    /// Allocate memory with the specified size and alignment.
    ///
    /// # Arguments
    ///
    /// * `size` - The size of the allocation in bytes.
    /// * `alignment` - The alignment of the allocation.
    /// * `offset` - Optional offset for the alignment.
    ///
    /// # Returns
    ///
    /// A pointer to the allocated memory, or `None` if allocation failed.
    fn alloc(&mut self, size: usize, alignment: usize, offset: usize) -> Option<*mut u8>;

    /// Allocate memory with the specified size and alignment.
    ///
    /// # Arguments
    ///
    /// * `size` - The size of the allocation in bytes.
    /// * `alignment` - The alignment of the allocation.
    ///
    /// # Returns
    ///
    /// A pointer to the allocated memory, or `None` if allocation failed.
    fn alloc_aligned(&mut self, size: usize, alignment: usize) -> Option<*mut u8> {
        self.alloc(size, alignment, 0)
    }

    /// Free memory previously allocated by this allocator.
    ///
    /// # Arguments
    ///
    /// * `ptr` - Pointer to the memory to free.
    /// * `size` - Size of the allocation.
    ///
    /// # Safety
    ///
    /// This function is unsafe because it deallocates memory.
    /// The caller must ensure that the pointer was allocated by this allocator
    /// and that it is not used after being freed.
    unsafe fn free(&mut self, ptr: *mut u8, size: usize);
}
