pub mod context;
pub mod platform;
pub mod platform_impl;

pub mod driver;
pub mod driver_impl;
pub mod swapchain;
pub mod swapchain_impl;
pub mod texture;
pub mod uniform_buffer;
pub mod vertex_buffer;

// Re-export SwapChain
pub use swapchain_impl::SwapChain;

#[allow(unused_imports)]
pub mod prelude {
    pub use super::driver::*;
    pub use super::platform::*;
    pub use super::vertex_buffer::*;
    pub use crate::backend::driver_enums::*;
}
