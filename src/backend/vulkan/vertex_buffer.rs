use crate::backend::vulkan::driver::VulkanDriver;
use ash::{vk, Devi<PERSON>};
use ash::vk::Handle;
use std::mem::{offset_of, size_of};
use std::collections::HashMap;
use tracing::debug;
use crate::utils::fs;

/// Holds a set of buffers that define the geometry of a Renderable.
///
/// The geometry of the Renderable itself is defined by a set of vertex attributes such as
/// position, color, normals, tangents, etc...
///
/// There is no need to have a 1-to-1 mapping between attributes and buffer. A buffer can hold the
/// data of several attributes -- attributes are then referred as being "interleaved".
///
/// The buffers themselves are GPU resources, therefore mutating their data can be relatively slow.
/// For this reason, it is best to separate the constant data from the dynamic data into multiple
/// buffers.
pub struct VertexBuffer {
    vertex_buffer: vk::Buffer,
    vertex_buffer_memory: vk::DeviceMemory,
    index_buffer: vk::Buffer,
    index_buffer_memory: vk::DeviceMemory,
    index_count: usize,
}

#[derive(<PERSON><PERSON>, Copy)]
#[allow(dead_code)]
#[repr(C)]
pub struct Vertex {
    pub pos: [f32; 3],
    pub color: [f32; 3],
    pub coords: [f32; 2],
}

impl Vertex {
    pub fn get_binding_description() -> vk::VertexInputBindingDescription {
        vk::VertexInputBindingDescription::default()
            .binding(0)
            .stride(size_of::<Vertex>() as _)
            .input_rate(vk::VertexInputRate::VERTEX)
    }

    pub fn get_attribute_descriptions() -> [vk::VertexInputAttributeDescription; 3] {
        let position_desc = vk::VertexInputAttributeDescription::default()
            .binding(0)
            .location(0)
            .format(vk::Format::R32G32B32_SFLOAT)
            .offset(offset_of!(Vertex, pos) as _);
        let color_desc = vk::VertexInputAttributeDescription::default()
            .binding(0)
            .location(1)
            .format(vk::Format::R32G32B32_SFLOAT)
            .offset(offset_of!(Vertex, color) as _);
        let coords_desc = vk::VertexInputAttributeDescription::default()
            .binding(0)
            .location(2)
            .format(vk::Format::R32G32_SFLOAT)
            .offset(offset_of!(Vertex, coords) as _);
        [position_desc, color_desc, coords_desc]
    }
}

impl VertexBuffer {
    /// 创建一个空的顶点缓冲区，用于测试和占位
    pub fn empty() -> Self {
        VertexBuffer {
            vertex_buffer: vk::Buffer::null(),
            vertex_buffer_memory: vk::DeviceMemory::null(),
            index_buffer: vk::Buffer::null(),
            index_buffer_memory: vk::DeviceMemory::null(),
            index_count: 0,
        }
    }

    pub fn new(
        vulkan_driver: &VulkanDriver,
        command_pool: vk::CommandPool,
        transfer_queue: vk::Queue,
        vertices: &[Vertex],
        indices: &[u32],
    ) -> Self {
        let (vertex_buffer, vertex_buffer_memory) = Self::create_vertex_buffer(
            vulkan_driver,
            command_pool,
            transfer_queue,
            vertices,
        );

        let (index_buffer, index_buffer_memory) = Self::create_index_buffer(
            vulkan_driver,
            command_pool,
            transfer_queue,
            indices,
        );

        VertexBuffer {
            vertex_buffer,
            vertex_buffer_memory,
            index_buffer,
            index_buffer_memory,
            index_count: indices.len(),
        }
    }

    pub fn from_obj_file(
        vulkan_driver: &VulkanDriver,
        command_pool: vk::CommandPool,
        transfer_queue: vk::Queue,
        obj_path: &str,
    ) -> Self {
        let (vertices, indices) = Self::load_model(obj_path);
        Self::new(vulkan_driver, command_pool, transfer_queue, &vertices, &indices)
    }

    fn create_vertex_buffer(
        vulkan_driver: &VulkanDriver,
        command_pool: vk::CommandPool,
        transfer_queue: vk::Queue,
        vertices: &[Vertex],
    ) -> (vk::Buffer, vk::DeviceMemory) {
        // 获取设备和上下文
        let device = vulkan_driver.device().expect("Device not available");
        let context = vulkan_driver.context().expect("Context not available");

        // 创建缓冲区
        let size = std::mem::size_of_val(vertices) as vk::DeviceSize;

        // 创建暂存缓冲区
        let staging_buffer_info = vk::BufferCreateInfo::default()
            .size(size)
            .usage(vk::BufferUsageFlags::TRANSFER_SRC)
            .sharing_mode(vk::SharingMode::EXCLUSIVE);

        let staging_buffer = unsafe {
            device.create_buffer(&staging_buffer_info, None).unwrap()
        };

        // 获取内存需求
        let mem_requirements = unsafe {
            device.get_buffer_memory_requirements(staging_buffer)
        };

        // 分配内存
        let mem_type = Self::find_memory_type(
            mem_requirements,
            context.get_mem_properties(),
            vk::MemoryPropertyFlags::HOST_VISIBLE | vk::MemoryPropertyFlags::HOST_COHERENT,
        );

        let alloc_info = vk::MemoryAllocateInfo::default()
            .allocation_size(mem_requirements.size)
            .memory_type_index(mem_type);

        let staging_memory = unsafe {
            device.allocate_memory(&alloc_info, None).unwrap()
        };

        // 绑定内存到缓冲区
        unsafe {
            device.bind_buffer_memory(staging_buffer, staging_memory, 0).unwrap()
        };

        // 复制数据到暂存缓冲区
        unsafe {
            let data_ptr = device
                .map_memory(staging_memory, 0, size, vk::MemoryMapFlags::empty())
                .unwrap();
            let mut align = ash::util::Align::new(data_ptr, std::mem::align_of::<u32>() as _, mem_requirements.size);
            align.copy_from_slice(vertices);
            device.unmap_memory(staging_memory);
        };

        // 创建设备本地缓冲区
        let buffer_info = vk::BufferCreateInfo::default()
            .size(size)
            .usage(vk::BufferUsageFlags::TRANSFER_DST | vk::BufferUsageFlags::VERTEX_BUFFER)
            .sharing_mode(vk::SharingMode::EXCLUSIVE);

        let vertex_buffer = unsafe {
            device.create_buffer(&buffer_info, None).unwrap()
        };

        // 获取内存需求
        let mem_requirements = unsafe {
            device.get_buffer_memory_requirements(vertex_buffer)
        };

        // 分配内存
        let mem_type = Self::find_memory_type(
            mem_requirements,
            context.get_mem_properties(),
            vk::MemoryPropertyFlags::DEVICE_LOCAL,
        );

        let alloc_info = vk::MemoryAllocateInfo::default()
            .allocation_size(mem_requirements.size)
            .memory_type_index(mem_type);

        let vertex_memory = unsafe {
            device.allocate_memory(&alloc_info, None).unwrap()
        };

        // 绑定内存到缓冲区
        unsafe {
            device.bind_buffer_memory(vertex_buffer, vertex_memory, 0).unwrap()
        };

        // 复制数据从暂存缓冲区到设备本地缓冲区
        Self::copy_buffer(
            device,
            command_pool,
            transfer_queue,
            staging_buffer,
            vertex_buffer,
            size,
        );

        // 清理暂存缓冲区
        unsafe {
            device.destroy_buffer(staging_buffer, None);
            device.free_memory(staging_memory, None);
        };

        (vertex_buffer, vertex_memory)
    }

    fn create_index_buffer(
        vulkan_driver: &VulkanDriver,
        command_pool: vk::CommandPool,
        transfer_queue: vk::Queue,
        indices: &[u32],
    ) -> (vk::Buffer, vk::DeviceMemory) {
        // 获取设备和上下文
        let device = vulkan_driver.device().expect("Device not available");
        let context = vulkan_driver.context().expect("Context not available");

        // 创建缓冲区
        let size = std::mem::size_of_val(indices) as vk::DeviceSize;

        // 创建暂存缓冲区
        let staging_buffer_info = vk::BufferCreateInfo::default()
            .size(size)
            .usage(vk::BufferUsageFlags::TRANSFER_SRC)
            .sharing_mode(vk::SharingMode::EXCLUSIVE);

        let staging_buffer = unsafe {
            device.create_buffer(&staging_buffer_info, None).unwrap()
        };

        // 获取内存需求
        let mem_requirements = unsafe {
            device.get_buffer_memory_requirements(staging_buffer)
        };

        // 分配内存
        let mem_type = Self::find_memory_type(
            mem_requirements,
            context.get_mem_properties(),
            vk::MemoryPropertyFlags::HOST_VISIBLE | vk::MemoryPropertyFlags::HOST_COHERENT,
        );

        let alloc_info = vk::MemoryAllocateInfo::default()
            .allocation_size(mem_requirements.size)
            .memory_type_index(mem_type);

        let staging_memory = unsafe {
            device.allocate_memory(&alloc_info, None).unwrap()
        };

        // 绑定内存到缓冲区
        unsafe {
            device.bind_buffer_memory(staging_buffer, staging_memory, 0).unwrap()
        };

        // 复制数据到暂存缓冲区
        unsafe {
            let data_ptr = device
                .map_memory(staging_memory, 0, size, vk::MemoryMapFlags::empty())
                .unwrap();
            let mut align = ash::util::Align::new(data_ptr, std::mem::align_of::<u16>() as _, mem_requirements.size);
            align.copy_from_slice(indices);
            device.unmap_memory(staging_memory);
        };

        // 创建设备本地缓冲区
        let buffer_info = vk::BufferCreateInfo::default()
            .size(size)
            .usage(vk::BufferUsageFlags::TRANSFER_DST | vk::BufferUsageFlags::INDEX_BUFFER)
            .sharing_mode(vk::SharingMode::EXCLUSIVE);

        let index_buffer = unsafe {
            device.create_buffer(&buffer_info, None).unwrap()
        };

        // 获取内存需求
        let mem_requirements = unsafe {
            device.get_buffer_memory_requirements(index_buffer)
        };

        // 分配内存
        let mem_type = Self::find_memory_type(
            mem_requirements,
            context.get_mem_properties(),
            vk::MemoryPropertyFlags::DEVICE_LOCAL,
        );

        let alloc_info = vk::MemoryAllocateInfo::default()
            .allocation_size(mem_requirements.size)
            .memory_type_index(mem_type);

        let index_memory = unsafe {
            device.allocate_memory(&alloc_info, None).unwrap()
        };

        // 绑定内存到缓冲区
        unsafe {
            device.bind_buffer_memory(index_buffer, index_memory, 0).unwrap()
        };

        // 复制数据从暂存缓冲区到设备本地缓冲区
        Self::copy_buffer(
            device,
            command_pool,
            transfer_queue,
            staging_buffer,
            index_buffer,
            size,
        );

        // 清理暂存缓冲区
        unsafe {
            device.destroy_buffer(staging_buffer, None);
            device.free_memory(staging_memory, None);
        };

        (index_buffer, index_memory)
    }

    /// Load a 3D model from an OBJ file.
    fn load_model(obj_path: &str) -> (Vec<Vertex>, Vec<u32>) {
        debug!("Loading model from {}", obj_path);
        let mut cursor = fs::load(obj_path);
        let (models, _) = tobj::load_obj_buf(
            &mut cursor,
            &tobj::LoadOptions {
                single_index: true,
                triangulate: true,
                ..Default::default()
            },
            |_| Ok((vec![], HashMap::<String, usize>::new())),
        )
        .unwrap();

        let mesh = &models[0].mesh;
        let positions = mesh.positions.as_slice();
        let coords = mesh.texcoords.as_slice();
        let vertex_count = mesh.positions.len() / 3;

        let mut vertices = Vec::with_capacity(vertex_count);
        for i in 0..vertex_count {
            let x = positions[i * 3];
            let y = positions[i * 3 + 1];
            let z = positions[i * 3 + 2];
            let u = coords[i * 2];
            let v = coords[i * 2 + 1];

            let vertex = Vertex {
                pos: [x, y, z],
                color: [1.0, 1.0, 1.0],
                coords: [u, v],
            };
            vertices.push(vertex);
        }

        (vertices, mesh.indices.clone())
    }

    // 注意：create_device_local_buffer_with_data 和 create_buffer 方法已被内联到 create_vertex_buffer 和 create_index_buffer 方法中

    /// Copy data from one buffer to another.
    fn copy_buffer(
        device: &Device,
        command_pool: vk::CommandPool,
        transfer_queue: vk::Queue,
        src_buffer: vk::Buffer,
        dst_buffer: vk::Buffer,
        size: vk::DeviceSize,
    ) {
        Self::execute_one_time_commands(device, command_pool, transfer_queue, |command_buffer| {
            let copy_region = vk::BufferCopy {
                src_offset: 0,
                dst_offset: 0,
                size,
            };
            let regions = [copy_region];
            unsafe { device.cmd_copy_buffer(command_buffer, src_buffer, dst_buffer, &regions) };
        });
    }

    /// Execute a one time command.
    fn execute_one_time_commands<F>(
        device: &Device,
        command_pool: vk::CommandPool,
        queue: vk::Queue,
        executor: F,
    ) where
        F: FnOnce(vk::CommandBuffer),
    {
        let alloc_info = vk::CommandBufferAllocateInfo::default()
            .level(vk::CommandBufferLevel::PRIMARY)
            .command_pool(command_pool)
            .command_buffer_count(1);

        let command_buffer = unsafe { device.allocate_command_buffers(&alloc_info).unwrap()[0] };
        let command_buffers = [command_buffer];

        let begin_info = vk::CommandBufferBeginInfo::default()
            .flags(vk::CommandBufferUsageFlags::ONE_TIME_SUBMIT);

        unsafe {
            device.begin_command_buffer(command_buffer, &begin_info).unwrap();
        }

        executor(command_buffer);

        unsafe {
            device.end_command_buffer(command_buffer).unwrap();

            let submit_info = vk::SubmitInfo::default().command_buffers(&command_buffers);
            let submit_infos = [submit_info];
            device
                .queue_submit(queue, &submit_infos, vk::Fence::null())
                .unwrap();
            device.queue_wait_idle(queue).unwrap();

            device.free_command_buffers(command_pool, &command_buffers);
        }
    }

    /// Find a memory type that has all the requested properties.
    fn find_memory_type(
        mem_requirements: vk::MemoryRequirements,
        mem_properties: vk::PhysicalDeviceMemoryProperties,
        required_properties: vk::MemoryPropertyFlags,
    ) -> u32 {
        for i in 0..mem_properties.memory_type_count {
            let memory_type = mem_properties.memory_types[i as usize];
            let type_supported = (mem_requirements.memory_type_bits & (1 << i)) != 0;
            let properties_supported = memory_type.property_flags.contains(required_properties);

            if type_supported && properties_supported {
                return i;
            }
        }

        panic!("Failed to find suitable memory type.")
    }

    // Getters

    pub fn vertex_buffer(&self) -> vk::Buffer {
        self.vertex_buffer
    }

    pub fn index_buffer(&self) -> vk::Buffer {
        self.index_buffer
    }

    pub fn index_count(&self) -> usize {
        self.index_count
    }

    /// 获取顶点缓冲区ID
    pub fn get_vertex_buffer_id(&self) -> u64 {
        // 在实际应用中，这应该返回一个有效的缓冲区ID
        // 这里简单地将VkBuffer转换为u64
        self.vertex_buffer.as_raw()
    }

    /// 获取索引缓冲区ID
    pub fn get_index_buffer_id(&self) -> u64 {
        // 在实际应用中，这应该返回一个有效的缓冲区ID
        // 这里简单地将VkBuffer转换为u64
        self.index_buffer.as_raw()
    }

    /// 获取索引计数
    pub fn get_index_count(&self) -> usize {
        self.index_count
    }

    // Cleanup

    pub fn destroy(&mut self, device: &Device) {
        unsafe {
            device.destroy_buffer(self.vertex_buffer, None);
            device.free_memory(self.vertex_buffer_memory, None);
            device.destroy_buffer(self.index_buffer, None);
            device.free_memory(self.index_buffer_memory, None);
        }
    }
}

impl Drop for VertexBuffer {
    fn drop(&mut self) {
        // Resources will be cleaned up by the explicit destroy method
        // This is just to prevent resource leaks if destroy is not called
        debug!("VertexBuffer dropped without explicit destroy call");
    }
}
