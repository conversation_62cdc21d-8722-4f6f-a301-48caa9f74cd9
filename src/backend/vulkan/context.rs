use ash::{Instance, vk};
use std::sync::Arc;
use tracing::debug;

pub struct VulkanContext {
    instance: Arc<Instance>,
    physical_device: vk::PhysicalDevice,
    physical_device_memory_props_2: vk::PhysicalDeviceMemoryProperties2<'static>,
    physical_device_properties_2: vk::PhysicalDeviceProperties2<'static>,
    physical_device_vk13_features: vk::PhysicalDeviceVulkan13Features<'static>,
    physical_device_features_2: vk::PhysicalDeviceFeatures2<'static>,
    debug_marker_supported: bool,
    debug_utils_supported: bool,
}

impl VulkanContext {
    pub fn new(instance: Arc<Instance>, physical_device: vk::PhysicalDevice) -> Self {
        debug!("Creating Vulkan context.");

        // 获取物理设备的内存属性
        let mut physical_device_memory_props_2 = vk::PhysicalDeviceMemoryProperties2::default();
        unsafe {
            instance.get_physical_device_memory_properties2(
                physical_device,
                &mut physical_device_memory_props_2,
            );
        }

        // 获取物理设备的属性
        let mut physical_device_properties_2 = vk::PhysicalDeviceProperties2::default();
        unsafe {
            instance.get_physical_device_properties2(
                physical_device,
                &mut physical_device_properties_2,
            );
        }

        // 获取物理设备的Vulkan 1.3特性
        let mut physical_device_vk13_features = vk::PhysicalDeviceVulkan13Features::default();
        let mut physical_device_features_2 = vk::PhysicalDeviceFeatures2::default();
        physical_device_features_2.p_next = &mut physical_device_vk13_features as *mut _ as *mut std::ffi::c_void;
        unsafe {
            instance.get_physical_device_features2(
                physical_device,
                &mut physical_device_features_2,
            );
        }

        // 检查是否支持调试标记和调试工具
        let debug_marker_supported = false; // 这里可以添加检查逻辑
        let debug_utils_supported = true;   // 这里可以添加检查逻辑

        VulkanContext {
            instance,
            physical_device,
            physical_device_memory_props_2,
            physical_device_properties_2,
            physical_device_vk13_features,
            physical_device_features_2,
            debug_marker_supported,
            debug_utils_supported,
        }
    }

    pub fn select_memory_type_with_props(
        memory_props: vk::PhysicalDeviceMemoryProperties2<'static>,
        mut flags: u32,
        reqs: vk::Flags,
    ) -> u32 {
        for i in 0..vk::MAX_MEMORY_TYPES {
            if (flags & 1) != 0 {
                // find a memory type that matches the requirements
                if (memory_props.memory_properties.memory_types[i]
                    .property_flags
                    .as_raw()
                    & reqs)
                    == reqs
                {
                    return i as u32;
                }
            }
            flags >>= 1;
        }
        vk::MAX_MEMORY_TYPES as u32
    }

    pub fn select_memory_type(&self, flags: u32, reqs: vk::Flags) -> u32 {
        Self::select_memory_type_with_props(self.physical_device_memory_props_2, flags, reqs)
    }

    pub fn get_physical_device_limits(&self) -> vk::PhysicalDeviceLimits {
        self.physical_device_properties_2.properties.limits
    }

    pub fn get_physical_device_vendor_id(&self) -> u32 {
        self.physical_device_properties_2.properties.vendor_id
    }

    pub fn get_mem_properties(&self) -> vk::PhysicalDeviceMemoryProperties {
        self.physical_device_memory_props_2.memory_properties
    }

    pub fn get_max_usable_sample_count(&self) -> vk::SampleCountFlags {
        let props = self.physical_device_properties_2.properties;
        let color_sample_counts = props.limits.framebuffer_color_sample_counts;
        let depth_sample_counts = props.limits.framebuffer_depth_sample_counts;
        let sample_counts = color_sample_counts.min(depth_sample_counts);

        if sample_counts.contains(vk::SampleCountFlags::TYPE_64) {
            vk::SampleCountFlags::TYPE_64
        } else if sample_counts.contains(vk::SampleCountFlags::TYPE_32) {
            vk::SampleCountFlags::TYPE_32
        } else if sample_counts.contains(vk::SampleCountFlags::TYPE_16) {
            vk::SampleCountFlags::TYPE_16
        } else if sample_counts.contains(vk::SampleCountFlags::TYPE_8) {
            vk::SampleCountFlags::TYPE_8
        } else if sample_counts.contains(vk::SampleCountFlags::TYPE_4) {
            vk::SampleCountFlags::TYPE_4
        } else if sample_counts.contains(vk::SampleCountFlags::TYPE_2) {
            vk::SampleCountFlags::TYPE_2
        } else {
            vk::SampleCountFlags::TYPE_1
        }
    }

    /// Find the first compatible format from `candidates`.
    pub fn find_supported_format(
        &self,
        candidates: &[vk::Format],
        tiling: vk::ImageTiling,
        features: vk::FormatFeatureFlags,
    ) -> Option<vk::Format> {
        candidates.iter().cloned().find(|candidate| {
            let props = unsafe {
                self.instance
                    .get_physical_device_format_properties(self.physical_device, *candidate)
            };
            (tiling == vk::ImageTiling::LINEAR && props.linear_tiling_features.contains(features))
                || (tiling == vk::ImageTiling::OPTIMAL
                    && props.optimal_tiling_features.contains(features))
        })
    }

    pub fn is_debug_marker_supported(&self) -> bool {
        self.debug_marker_supported
    }

    pub fn is_debug_utils_supported(&self) -> bool {
        self.debug_utils_supported
    }

    pub fn instance(&self) -> &Instance {
        &self.instance
    }

    pub fn physical_device(&self) -> vk::PhysicalDevice {
        self.physical_device
    }
}
